import React from 'react';
import { cn } from '../../utils/cn';
import { Search, ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from './Button';
import { Input } from './Input';

interface TableProps<T> {
  data: T[];
  columns: {
    key: string;
    header: string;
    render?: (value: any, row: T) => React.ReactNode;
  }[];
  isLoading?: boolean;
  pagination?: {
    page: number;
    pageSize: number;
    total: number;
    onPageChange: (page: number) => void;
    onPageSizeChange: (pageSize: number) => void;
  };
  searchable?: boolean;
  onSearch?: (query: string) => void;
  className?: string;
  stickyHeader?: boolean;
  stickyFooter?: boolean;
}

export function Table<T extends Record<string, any>>({
  data,
  columns,
  isLoading = false,
  pagination,
  searchable = false,
  onSearch,
  className,
  stickyHeader = false,
  stickyFooter = false,
}: TableProps<T>) {
  const [searchQuery, setSearchQuery] = React.useState('');

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
    if (onSearch) {
      onSearch(query);
    }
  };

  const pageSizeOptions = [10, 20, 50, 100];

  return (
    <div className={cn('w-full relative', className)}>
      {searchable && (
        <div className="mb-4">
          <Input
            placeholder="Search..."
            value={searchQuery}
            onChange={handleSearch}
            leftIcon={<Search size={18} />}
            className="max-w-sm"
          />
        </div>
      )}

      <div className="relative overflow-hidden rounded-lg">
        <div className="overflow-auto max-h-[calc(70vh-120px)]">
          <table className="w-full min-w-full table-auto border-collapse">
            <thead className={cn(
              "whitespace-nowrap bg-muted text-muted-foreground",
              stickyHeader && "sticky top-0 z-20"
            )}>
              <tr>
                {columns.map((column, index) => (
                  <th
                    key={column.key}
                    className={cn(
                      "px-4 py-3 text-left text-sm font-medium",
                      index === 0 && "sticky left-0 z-10 bg-muted"
                    )}
                  >
                    {column.header}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="whitespace-nowrap divide-y">
              {isLoading ? (
                <tr>
                  <td
                    colSpan={columns.length}
                    className="px-4 py-4 text-center text-sm"
                  >
                    <div className="flex justify-center items-center space-x-2">
                      <svg
                        className="animate-spin h-5 w-5 text-primary"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      <span>Loading data...</span>
                    </div>
                  </td>
                </tr>
              ) : data.length === 0 ? (
                <tr>
                  <td
                    colSpan={columns.length}
                    className="px-4 py-4 text-center text-sm text-muted-foreground"
                  >
                    No data available
                  </td>
                </tr>
              ) : (
                data.map((row, rowIndex) => (
                  <tr
                    key={rowIndex}
                    className="bg-card hover:bg-muted/50 transition-colors"
                  >
                    {columns.map((column, colIndex) => (
                      <td
                        key={`${rowIndex}-${column.key}`}
                        className={cn(
                          "px-4 py-3 text-sm",
                          colIndex === 0 && "sticky left-0 z-10 bg-card"
                        )}
                      >
                        {column.render
                          ? column.render(row[column.key], row)
                          : row[column.key]}
                      </td>
                    ))}
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {pagination && (
          <div className={cn(
            "flex items-center justify-between px-4 py-3 border-t bg-card",
            stickyFooter && "sticky bottom-0 z-20 shadow-[0_-2px_5px_rgba(0,0,0,0.1)]"
          )}>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground">Rows per page:</span>
              <select
                className="h-8 rounded-md border border-input bg-background px-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring"
                value={pagination.pageSize}
                onChange={(e) => pagination.onPageSizeChange(Number(e.target.value))}
              >
                {pageSizeOptions.map((size) => (
                  <option key={size} value={size}>
                    {size}
                  </option>
                ))}
              </select>
            </div>

            <div className="flex items-center space-x-6">
              <span className="text-sm text-muted-foreground">
                {pagination.page * pagination.pageSize - pagination.pageSize + 1}-
                {Math.min(pagination.page * pagination.pageSize, pagination.total)} of{' '}
                {pagination.total}
              </span>

              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => pagination.onPageChange(pagination.page - 1)}
                  disabled={pagination.page <= 1}
                  leftIcon={<ChevronLeft size={16} />}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => pagination.onPageChange(pagination.page + 1)}
                  disabled={
                    pagination.page >= Math.ceil(pagination.total / pagination.pageSize)
                  }
                  rightIcon={<ChevronRight size={16} />}
                >
                  Next
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}