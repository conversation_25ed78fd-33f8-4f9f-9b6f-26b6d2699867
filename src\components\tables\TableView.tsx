import React, { useCallback, useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, RefreshCw, Search } from 'lucide-react';
import { Button } from '../ui/Button';
import { Table } from '../ui/Table';
import { tableService } from '../../services/api';
import { PaginationParams, TableData, TableInfo } from '../../types';
import { Input } from '../ui/Input';
import { Card, CardDescription, CardHeader, CardTitle } from '../ui/Card';

export const TableView: React.FC = () => {
  const { tableId } = useParams<{ tableId: string }>();
  const navigate = useNavigate();
  const [tableInfo, setTableInfo] = useState<TableInfo | null>(null);
  const [tableData, setTableData] = useState<TableData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [paginationParams, setPaginationParams] = useState<PaginationParams>({
    page: 1,
    rows: 20,
  });

  const fetchTableInfo = useCallback(async () => {
    setIsLoading(true);
    try {
      const tables = await tableService.getTables();
      const table = tables.find((t) => t.id === tableId);
      if (table) {
        setTableInfo(table);
      } else {
        setError('Table not found');
      }
    } catch (err) {
      setError('Failed to load table information');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  }, [tableId]);

  const fetchTableData = useCallback(async () => {
    if (!tableId) return;

    setIsLoading(true);
    setError('');
    try {
      const params: PaginationParams = {
        ...paginationParams,
        search: searchQuery || undefined,
      };
      const data = await tableService.getTableData(tableId, params);
      setTableData(data);
    } catch (err) {
      setError('Failed to load table data');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  }, [paginationParams, searchQuery, tableId]);

  useEffect(() => {
    if (tableId) {
      fetchTableInfo();
    }
  }, [fetchTableInfo, tableId]);

  useEffect(() => {
    if (tableId) {
      fetchTableData();
    }
  }, [fetchTableData, tableId, paginationParams.page, paginationParams.rows]);

  const handleSearch = () => {
    setPaginationParams((prev) => ({ ...prev, page: 1 }));
    fetchTableData();
  };

  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const handleSearchKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const handlePageChange = (page: number) => {
    setPaginationParams((prev) => ({ ...prev, page }));
  };

  const handlePageSizeChange = (rows: number) => {
    setPaginationParams((prev) => ({ ...prev, page: 1, rows }));
  };

  const handleRefresh = () => {
    fetchTableData();
  };

  const handleBackToDashboard = () => {
    navigate('/dashboard');
  };

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md max-w-md">
          <p className="font-medium">Error</p>
          <p className="text-sm">{error}</p>
          <Button
            variant="outline"
            size="sm"
            className="mt-4"
            onClick={handleBackToDashboard}
            leftIcon={<ArrowLeft size={16} />}
          >
            Back to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center mb-6">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleBackToDashboard}
          leftIcon={<ArrowLeft size={16} />}
        >
          Back to Dashboard
        </Button>
      </div>

      <Card className="mb-6 border-2 border-primary/20 dark:border-primary/30 bg-white/80 dark:bg-gray-800/80">
        <CardHeader className="text-center">
          <CardTitle className="text-3xl font-bold tracking-tight">
            {tableInfo?.name || 'Loading...'}
          </CardTitle>
          <CardDescription className="text-lg">
            {tableInfo?.description || 'Loading table information...'}
          </CardDescription>
        </CardHeader>
      </Card>

      <div className="flex flex-col md:flex-row md:items-center gap-4 mb-4 w-full">
        {/* Search and Button Container */}
        <div className="flex w-full mx-auto items-center space-x-2">
          <Input
            placeholder="Search table..."
            value={searchQuery}
            onChange={handleSearchInputChange}
            onKeyDown={handleSearchKeyDown}
            leftIcon={<Search size={18} />}
          />
          <Button
            variant="outline"
            onClick={handleSearch}
            className="ml-auto"
          >
            Search
          </Button>
          {/* Refresh Button */}
          <Button
            onClick={handleRefresh}
            leftIcon={<RefreshCw size={16} />}
            variant="outline"
            className="ml-auto" // This will push the button to the right
          >
            Refresh
          </Button>
        </div>
      </div>
      <div className="rounded-lg border-2 border-primary/20 bg-card shadow-md overflow-hidden">
        {tableData ? (
          <Table
            data={tableData.rows}
            columns={tableData.columns.map((col) => ({
              key: col,
              header: col.charAt(0).toUpperCase() + col.slice(1).replace(/_/g, ' '),
            }))}
            isLoading={isLoading}
            pagination={{
              page: paginationParams.page,
              pageSize: paginationParams.rows,
              total: tableData.total,
              onPageChange: handlePageChange,
              onPageSizeChange: handlePageSizeChange,
            }}
            stickyHeader
            stickyFooter
          />
        ) : (
          <div className="flex justify-center items-center h-64">
            <div className="flex flex-col items-center space-y-4">
              <svg
                className="animate-spin h-8 w-8 text-primary"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              <p className="text-lg">Loading table data...</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};