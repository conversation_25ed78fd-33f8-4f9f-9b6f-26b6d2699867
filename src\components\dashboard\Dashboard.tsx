import React, { useCallback, useEffect, useState } from 'react';
import { tableService } from '../../services/api';
import { TableInfo } from '../../types';
import { TableCard } from './TableCard';
import { Database } from 'lucide-react';
import { Card, CardContent } from '../ui/Card';

export const Dashboard: React.FC = () => {
  const [tables, setTables] = useState<TableInfo[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const fetchTables = useCallback(async () => {
    setIsLoading(true);
    setError('');
    try {
      const data = await tableService.getTables();
      setTables(data);
    } catch (err) {
      setError('Failed to load tables. Please try again later.');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchTables();
  }, [fetchTables]);

  const handleRefreshTable = () => {
    fetchTables();
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="flex flex-col items-center space-y-4">
          <svg
            className="animate-spin h-8 w-8 text-primary"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          <p className="text-lg text-foreground">Loading tables...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="bg-destructive/10 border border-destructive/20 text-destructive px-4 py-3 rounded-md max-w-md">
          <p className="font-medium">Error</p>
          <p className="text-sm">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card className="dashboard-card overflow-hidden">
        <CardContent className="pt-6 text-center dashboard-card-header">
          <h1 className="text-4xl font-bold tracking-tight text-foreground mb-2 bg-gradient-to-r from-primary via-secondary to-accent bg-clip-text text-transparent">
            Dashboard
          </h1>
          <p className="text-lg text-muted-foreground">
            View and manage your data tables
          </p>
        </CardContent>
      </Card>

      {tables.length === 0 ? (
        <div className="flex flex-col items-center justify-center h-64 dashboard-card rounded-lg">
          <Database className="h-12 w-12 text-primary mb-4" />
          <h3 className="text-xl font-medium text-foreground">No tables found</h3>
          <p className="text-sm text-muted-foreground mt-2">
            There are no tables available in your database.
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {tables.map((table) => (
            <TableCard
              key={table.id}
              table={table}
              onRefresh={handleRefreshTable}
            />
          ))}
        </div>
      )}
    </div>
  );
}