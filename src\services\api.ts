import axios from 'axios';
import { useAuthStore } from '../store/auth-store';
import { PaginationParams, TableData, TableInfo, User } from '../types';

// Base API URL - replace with your actual API endpoint
const API_URL = 'https://api.example.com';

// Create axios instance
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to include auth token
api.interceptors.request.use(
  (config) => {
    const { user } = useAuthStore.getState();
    if (user?.token) {
      config.headers.Authorization = `Bearer ${user.token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Authentication services
export const authService = {
  login: async (username: string, password: string): Promise<User> => {
    // This is a mock implementation - replace with actual API call
    // In a real app, you would call your authentication endpoint
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          id: '1',
          username,
          email: `${username}@example.com`,
          token: 'mock-jwt-token',
        });
      }, 500);
    });
  },
};

// Table services
export const tableService = {
  // Get all available tables
  getTables: async (): Promise<TableInfo[]> => {
    // Mock implementation - replace with actual API call
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve([
          {
            id: '1',
            name: 'Users',
            description: 'User accounts and profile information',
            endpoint: '/users',
          },
          {
            id: '2',
            name: 'Products',
            description: 'Product catalog with details and pricing',
            endpoint: '/products',
          },
          {
            id: '3',
            name: 'Orders',
            description: 'Customer orders and transaction history',
            endpoint: '/orders',
          },
          {
            id: '4',
            name: 'Inventory',
            description: 'Current stock levels and warehouse information',
            endpoint: '/inventory',
          },
          {
            id: '5',
            name: 'Customers',
            description: 'Customer information and purchase history',
            endpoint: '/customers',
          },
          {
            id: '6',
            name: 'Suppliers',
            description: 'Supplier details and contact information',
            endpoint: '/suppliers',
          },
        ]);
      }, 500);
    });
  },

  // Get table data with pagination
  getTableData: async (
    tableId: string,
    params: PaginationParams
  ): Promise<TableData> => {
    // Mock implementation - replace with actual API call
    return new Promise((resolve) => {
      setTimeout(() => {
        // Generate mock data based on the table ID
        const mockData = generateMockData(tableId, params);
        resolve(mockData);
      }, 800);
    });
  },
};

// Helper function to generate mock data for demonstration
function generateMockData(tableId: string, params: PaginationParams): TableData {
  const { page, rows, search } = params;
  let columns: string[] = [];
  let mockRows: Record<string, any>[] = [];
  const total = 10000; // Total number of records

  // Define columns based on table ID
  switch (tableId) {
    case '1': // Users
      columns = ['id', 'username', 'email', 'role', 'created_at', 'status'];
      break;
    case '2': // Products
      columns = ['id', 'name', 'category', 'price', 'stock', 'supplier'];
      break;
    case '3': // Orders
      columns = ['id', 'customer', 'date', 'total', 'status', 'items'];
      break;
    case '4': // Inventory
      columns = ['id', 'product', 'quantity', 'location', 'last_updated', 'status'];
      break;
    case '5': // Customers
      columns = ['id', 'name', 'email', 'phone', 'address', 'total_orders'];
      break;
    case '6': // Suppliers
      columns = ['id', 'name', 'contact', 'email', 'phone', 'address'];
      break;
    default:
      columns = ['id', 'name', 'description'];
  }

  // Generate rows based on pagination parameters
  for (let i = 0; i < rows; i++) {
    const rowIndex = (page - 1) * rows + i;
    if (rowIndex >= total) break;

    const row: Record<string, any> = {};
    columns.forEach((col) => {
      if (col === 'id') {
        row[col] = `${rowIndex + 1}`;
      } else if (col === 'username' || col === 'name') {
        row[col] = `Item ${rowIndex + 1}`;
      } else if (col === 'email') {
        row[col] = `user${rowIndex + 1}@example.com`;
      } else if (col === 'price') {
        row[col] = `$${(Math.random() * 1000).toFixed(2)}`;
      } else if (col === 'date' || col === 'created_at' || col === 'last_updated') {
        row[col] = new Date(Date.now() - Math.random() * 10000000000).toISOString().split('T')[0];
      } else if (col === 'status') {
        const statuses = ['Active', 'Inactive', 'Pending', 'Completed'];
        row[col] = statuses[Math.floor(Math.random() * statuses.length)];
      } else if (col === 'quantity' || col === 'stock' || col === 'total_orders' || col === 'items') {
        row[col] = Math.floor(Math.random() * 100);
      } else {
        row[col] = `Value ${rowIndex + 1} for ${col}`;
      }
    });

    // Apply search filter if provided
    if (!search || Object.values(row).some(val => 
      val.toString().toLowerCase().includes(search.toLowerCase())
    )) {
      mockRows.push(row);
    }
  }

  // If search is applied, we need to adjust the rows to match the requested count
  if (search) {
    // This is a simplified approach - in a real API, you'd handle this differently
    mockRows = mockRows.slice(0, rows);
  }

  return {
    columns,
    rows: mockRows,
    total: search ? mockRows.length * 10 : total, // Simulate filtered total
  };
}