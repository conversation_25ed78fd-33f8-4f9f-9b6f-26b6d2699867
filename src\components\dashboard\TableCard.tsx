import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ExternalLink, RefreshCw } from 'lucide-react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../ui/Card';
import { Button } from '../ui/Button';
import { TableInfo } from '../../types';

interface TableCardProps {
  table: TableInfo;
  onRefresh: () => void;
}

export const TableCard: React.FC<TableCardProps> = ({ table, onRefresh }) => {
  const navigate = useNavigate();

  const handleViewTable = () => {
    navigate(`/tables/${table.id}`);
  };

  return (
    <Card className="dashboard-card h-full flex flex-col group">
      <CardHeader className="dashboard-card-header">
        <CardTitle className="text-xl text-foreground group-hover:text-primary transition-colors">
          {table.name}
        </CardTitle>
        <CardDescription className="text-muted-foreground">
          {table.description}
        </CardDescription>
      </CardHeader>
      <CardContent className="flex-grow">
        <div className="text-sm text-muted-foreground">
          <p>Endpoint: {table.endpoint}</p>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between bg-muted/10">
        <Button
          variant="outline"
          size="md"
          onClick={onRefresh}
          leftIcon={<RefreshCw size={16} />}
          className="bg-primary text-primary-foreground"
        >
          Refresh
        </Button>
        <Button
          variant="outline"
          size="md"
          onClick={handleViewTable}
          rightIcon={<ExternalLink size={16} />}
          className="bg-primary text-primary-foreground"
        >
          View Table
        </Button>
      </CardFooter>
    </Card>
  );
};