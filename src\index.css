@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light mode - Modern Professional Palette */
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    
    /* Primary - Deep Blue */
    --primary: 221 83% 53%;
    --primary-foreground: 210 40% 98%;
    
    /* Secondary - Teal */
    --secondary: 173 58% 39%;
    --secondary-foreground: 0 0% 100%;
    
    /* Accent - Purple */
    --accent: 262 83% 58%;
    --accent-foreground: 210 40% 98%;
    
    /* Muted colors */
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    
    /* Success, Warning, Error */
    --success: 142 76% 36%;
    --success-foreground: 355 100% 97%;
    --warning: 38 92% 50%;
    --warning-foreground: 48 96% 89%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;
    
    /* Border and input */
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221 83% 53%;
    --radius: 0.75rem;
  }

  .dark {
    /* Dark mode - Sophisticated Dark Palette */
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 240 10% 3.9%;
    
    /* Primary - Bright Blue */
    --primary: 217 91% 60%;
    --primary-foreground: 240 10% 3.9%;
    
    /* Secondary - Cyan */
    --secondary: 185 57% 50%;
    --secondary-foreground: 240 10% 3.9%;
    
    /* Accent - Violet */
    --accent: 263 70% 50%;
    --accent-foreground: 0 0% 98%;
    
    /* Muted colors */
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    
    /* Success, Warning, Error */
    --success: 142 70% 45%;
    --success-foreground: 144 100% 97%;
    --warning: 38 92% 50%;
    --warning-foreground: 20 14.3% 4.1%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 85.7% 97.3%;
    
    /* Border and input */
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 217 91% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    min-height: 100vh;
  }
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 6px;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.5);
  border-radius: 6px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.7);
}

/* Glassmorphism effects */
.bg-glass {
  @apply bg-white/10 backdrop-blur-md border-white/20;
}

.dark .bg-glass {
  @apply bg-black/10 backdrop-blur-md border-white/10;
}

/* Dashboard card styles */
.dashboard-card {
  @apply bg-card border-2 border-primary/20 shadow-lg hover:shadow-xl transition-all duration-300;
}

.dark .dashboard-card {
  @apply border-primary/30;
}

.dashboard-card:hover {
  @apply transform -translate-y-1 scale-100;
}

.dashboard-card-header {
  @apply bg-muted/30;
}

/* Table styles */
.table-header-sticky {
  position: sticky;
  top: 0;
  z-index: 20;
  @apply bg-muted/95 backdrop-blur-sm shadow-md;
}

th.sticky,
td.sticky {
  position: sticky;
  left: 0;
  background: inherit;
  z-index: 10;
}

.sticky-footer {
  position: sticky;
  bottom: 0;
  @apply bg-card/95 backdrop-blur-sm shadow-[0_-2px_8px_rgba(0,0,0,0.1)] border-t;
}

.table-container {
  max-height: 70vh;
  overflow: auto;
  @apply rounded-lg border-2 border-primary/20 bg-card shadow-md;
}

thead th {
  @apply bg-muted text-muted-foreground font-semibold;
}

tbody tr {
  @apply transition-all duration-200 hover:bg-muted/50;
}

tbody tr:hover td.sticky {
  @apply bg-muted/50;
}

/* Card styles */
.card-title {
  @apply text-foreground;
}

.card-description {
  @apply text-muted-foreground;
}

/* Success, Warning, Error color utilities */
.text-success {
  color: hsl(var(--success));
}

.bg-success {
  background: hsl(var(--success));
  color: hsl(var(--success-foreground));
}

.text-warning {
  color: hsl(var(--warning));
}

.bg-warning {
  background: hsl(var(--warning));
  color: hsl(var(--warning-foreground));
}

.border-success {
  border-color: hsl(var(--success));
}

.border-warning {
  border-color: hsl(var(--warning));
}