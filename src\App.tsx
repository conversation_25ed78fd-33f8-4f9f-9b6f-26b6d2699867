import { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { LoginPage } from './pages/LoginPage';
import { DashboardPage } from './pages/DashboardPage';
import { TablePage } from './pages/TablePage';
import { Layout } from './components/layout/Layout';
import { AuthMiddleware } from './middleware/AuthMiddleware';
import { useThemeStore } from './store/theme-store';

function App() {
  const { theme } = useThemeStore();

  useEffect(() => {
    // Apply theme to document
    document.documentElement.classList.remove('light', 'dark');
    document.documentElement.classList.add(theme);
  }, [theme]);

  return (
    <Router>
      <AuthMiddleware>
        <Routes>
          <Route path="/login" element={<LoginPage />} />
          <Route element={<Layout />}>
            <Route path="/dashboard" element={<DashboardPage />} />
            <Route path="/tables/:tableId" element={<TablePage />} />
          </Route>
          <Route path="*" element={<Navigate to="/dashboard" replace />} />
        </Routes>
      </AuthMiddleware>
    </Router>
  );
}

export default App;