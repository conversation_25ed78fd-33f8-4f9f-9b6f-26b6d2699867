import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { LoginForm } from '../components/auth/LoginForm';
import { useAuthStore } from '../store/auth-store';
import { Database } from 'lucide-react';

export const LoginPage: React.FC = () => {
  const navigate = useNavigate();
  const { isAuthenticated } = useAuthStore();

  useEffect(() => {
    if (isAuthenticated) {
      navigate('/dashboard');
    }
  }, [isAuthenticated, navigate]);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4 bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-indigo-950">
      <div className="w-full max-w-md space-y-8">
        <div className="flex flex-col items-center justify-center text-center">
          <div className="flex items-center justify-center h-12 w-12 rounded-full bg-primary/10 mb-4">
            <Database className="h-6 w-6 text-primary" />
          </div>
          <h1 className="text-3xl font-bold">DataVista</h1>
          <p className="text-muted-foreground mt-2">
            Efficiently manage and visualize large datasets
          </p>
        </div>

        <LoginForm />

        <div className="text-center text-sm text-muted-foreground mt-6">
          <p>
            Demo credentials: <span className="font-medium">admin / password</span>
          </p>
        </div>
      </div>
    </div>
  );
};