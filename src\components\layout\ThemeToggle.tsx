import React from 'react';
import { Moon, Sun } from 'lucide-react';
import { Button } from '../ui/Button';
import { useThemeStore } from '../../store/theme-store';

export const ThemeToggle: React.FC = () => {
  const { theme, toggleTheme } = useThemeStore();

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={toggleTheme}
      aria-label="Toggle theme"
    >
      {theme === 'light' ? <Moon size={20} /> : <Sun size={20} />}
    </Button>
  );
};