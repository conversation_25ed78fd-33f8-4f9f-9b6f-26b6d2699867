{"name": "large-dataset-web-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}, "dependencies": {"lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.22.3", "zustand": "^4.5.2", "axios": "^1.10.0", "clsx": "^2.1.1", "tailwind-merge": "^2.2.1"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.1", "typescript": "^5.8.3", "typescript-eslint": "^8.36.0", "vite": "^5.4.2"}}