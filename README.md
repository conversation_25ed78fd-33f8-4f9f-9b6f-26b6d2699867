# Large-Dataset-Web-Application

This is a web application for efficiently managing and visualizing large datasets. It is built using React, TypeScript, and Vite.

## Features

-   Login and authentication
-   Dashboard with table cards
-   Table view with pagination and search
-   Refresh button to reload table data
-   Dark mode support
-   Responsive design

## Getting Started

To get started with the project, follow these steps:

1.  Clone the repository:

    ```bash
    git clone https://github.com/your-username/large-dataset-web-app.git
    ```

2.  Install dependencies:

    ```bash
    npm install
    ```

3.  Start the development server:

    ```bash
    npm run dev
    ```

4.  Open your browser and navigate to `http://localhost:3000` to view the application.

## License

This project is licensed under the MIT License.
